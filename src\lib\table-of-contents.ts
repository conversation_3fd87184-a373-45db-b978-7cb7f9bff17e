import { TableOfContentsItem } from '@/types';

/**
 * 生成目录数据的工具函数
 * 这个函数可以在服务器组件中使用，因为它不依赖客户端API
 */
export function generateTableOfContents(content: string): TableOfContentsItem[] {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: TableOfContentsItem[] = [];
  let match;

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const title = match[2].trim();
    const anchor = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();

    headings.push({
      id: `heading-${headings.length + 1}`,
      title,
      level,
      anchor,
    });
  }

  // 构建层级结构
  return buildHierarchy(headings);
}

/**
 * 构建目录的层级结构
 */
function buildHierarchy(headings: TableOfContentsItem[]): TableOfContentsItem[] {
  const result: TableOfContentsItem[] = [];
  const stack: TableOfContentsItem[] = [];

  for (const heading of headings) {
    // 找到合适的父级
    while (stack.length > 0 && stack[stack.length - 1].level >= heading.level) {
      stack.pop();
    }

    if (stack.length === 0) {
      // 顶级标题
      result.push(heading);
    } else {
      // 子标题
      const parent = stack[stack.length - 1];
      if (!parent.children) {
        parent.children = [];
      }
      parent.children.push(heading);
    }

    stack.push(heading);
  }

  return result;
}

/**
 * 从HTML内容生成目录
 * 用于处理已经渲染的HTML内容
 */
export function generateTableOfContentsFromHTML(htmlContent: string): TableOfContentsItem[] {
  // 匹配HTML标题标签
  const headingRegex = /<h([1-6])[^>]*id="([^"]*)"[^>]*>([^<]+)<\/h[1-6]>/gi;
  const headings: TableOfContentsItem[] = [];
  let match;

  while ((match = headingRegex.exec(htmlContent)) !== null) {
    const level = parseInt(match[1]);
    const anchor = match[2];
    const title = match[3].trim();

    headings.push({
      id: `heading-${headings.length + 1}`,
      title,
      level,
      anchor,
    });
  }

  return buildHierarchy(headings);
}

/**
 * 为HTML内容添加标题ID
 * 确保标题有正确的ID用于锚点导航
 */
export function addHeadingIds(htmlContent: string): string {
  return htmlContent.replace(
    /<h([1-6])([^>]*)>([^<]+)<\/h[1-6]>/gi,
    (match, level, attributes, title) => {
      // 检查是否已经有ID
      if (attributes.includes('id=')) {
        return match;
      }

      // 生成ID
      const id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

      return `<h${level}${attributes} id="${id}">${title}</h${level}>`;
    }
  );
}
