'use client';

import React, { useMemo } from 'react';
import { remark } from 'remark';
import remarkGfm from 'remark-gfm';
import remarkHtml from 'remark-html';
import { cn } from '@/lib/utils';

interface MarkdownContentProps {
  content: string;
  className?: string;
}

/**
 * Markdown内容渲染组件
 * 基于01-frontend-design-rules.md的博客文章设计规范
 */
export function MarkdownContent({ content, className }: MarkdownContentProps) {
  // 处理Markdown内容
  const processedContent = useMemo(() => {
    try {
      // 检查内容是否已经是HTML格式
      if (content.includes('<') && content.includes('>')) {
        // 如果已经是HTML，直接返回并添加标题ID
        return addHeadingIds(content);
      }

      // 如果是Markdown，转换为HTML
      const result = remark()
        .use(remarkGfm) // 支持GitHub Flavored Markdown
        .use(remarkHtml, { sanitize: false }) // 允许HTML标签
        .processSync(content);

      return addHeadingIds(String(result));
    } catch (error) {
      console.error('Error processing markdown:', error);
      // 如果处理失败，返回原始内容
      return content;
    }
  }, [content]);

  return (
    <article
      id="article"
      className={cn(
        // Medium风格的基础样式
        'prose prose-lg max-w-none',
        
        // 标题样式 - 基于01规范的博客文章设计
        '[&>h1]:font-serif [&>h1]:font-bold [&>h1]:text-mystical-900 [&>h1]:dark:text-white',
        '[&>h1]:text-4xl [&>h1]:mt-8 [&>h1]:mb-4 [&>h1]:leading-tight [&>h1]:tracking-tight',
        
        '[&>h2]:font-serif [&>h2]:font-bold [&>h2]:text-mystical-900 [&>h2]:dark:text-white',
        '[&>h2]:text-3xl [&>h2]:mt-8 [&>h2]:mb-4 [&>h2]:leading-tight [&>h2]:tracking-tight',
        
        '[&>h3]:font-serif [&>h3]:font-bold [&>h3]:text-mystical-900 [&>h3]:dark:text-white',
        '[&>h3]:text-2xl [&>h3]:mt-6 [&>h3]:mb-3 [&>h3]:leading-tight',
        
        '[&>h4]:font-serif [&>h4]:font-bold [&>h4]:text-mystical-900 [&>h4]:dark:text-white',
        '[&>h4]:text-xl [&>h4]:mt-6 [&>h4]:mb-3 [&>h4]:leading-tight',
        
        '[&>h5]:font-serif [&>h5]:font-bold [&>h5]:text-mystical-800 [&>h5]:dark:text-mystical-100',
        '[&>h5]:text-lg [&>h5]:mt-4 [&>h5]:mb-2',
        
        '[&>h6]:font-serif [&>h6]:font-bold [&>h6]:text-mystical-800 [&>h6]:dark:text-mystical-100',
        '[&>h6]:text-base [&>h6]:mt-4 [&>h6]:mb-2',

        // 段落样式 - Medium标准
        '[&>p]:mb-6 [&>p]:leading-7 [&>p]:text-mystical-800 [&>p]:dark:text-mystical-200',
        '[&>p]:text-lg [&>p]:font-serif',
        
        // 首段特殊样式
        '[&>p:first-of-type]:text-xl [&>p:first-of-type]:font-normal [&>p:first-of-type]:text-mystical-900 [&>p:first-of-type]:dark:text-white',

        // 列表样式
        '[&>ul]:mb-6 [&>ul]:pl-6',
        '[&>ul>li]:mb-2 [&>ul>li]:text-lg [&>ul>li]:leading-7 [&>ul>li]:text-mystical-800 [&>ul>li]:dark:text-mystical-200',
        '[&>ul>li]:list-disc [&>ul>li]:marker:text-mystical-500',
        
        '[&>ol]:mb-6 [&>ol]:pl-6',
        '[&>ol>li]:mb-2 [&>ol>li]:text-lg [&>ol>li]:leading-7 [&>ol>li]:text-mystical-800 [&>ol>li]:dark:text-mystical-200',
        '[&>ol>li]:list-decimal [&>ol>li]:marker:text-mystical-500 [&>ol>li]:marker:font-semibold',

        // 引用块样式
        '[&>blockquote]:text-xl [&>blockquote]:font-serif [&>blockquote]:italic',
        '[&>blockquote]:text-mystical-700 [&>blockquote]:dark:text-mystical-200',
        '[&>blockquote]:px-8 [&>blockquote]:py-6 [&>blockquote]:my-8',
        '[&>blockquote]:bg-mystical-50 [&>blockquote]:dark:bg-dark-800',
        '[&>blockquote]:border-l-4 [&>blockquote]:border-mystical-400',
        '[&>blockquote]:rounded-r-lg [&>blockquote]:leading-relaxed',

        // 代码样式
        '[&>pre]:bg-dark-900 [&>pre]:text-dark-100 [&>pre]:p-6 [&>pre]:rounded-lg',
        '[&>pre]:my-8 [&>pre]:overflow-auto [&>pre]:border [&>pre]:border-dark-700',
        '[&>pre]:text-sm [&>pre]:font-mono [&>pre]:leading-relaxed',
        
        '[&>p>code]:bg-mystical-100 [&>p>code]:dark:bg-dark-700',
        '[&>p>code]:text-mystical-800 [&>p>code]:dark:text-mystical-200',
        '[&>p>code]:px-2 [&>p>code]:py-1 [&>p>code]:rounded [&>p>code]:text-sm',
        '[&>p>code]:font-mono [&>p>code]:border [&>p>code]:border-mystical-200 [&>p>code]:dark:border-dark-600',

        // 图片样式
        '[&>p>img]:w-full [&>p>img]:h-auto [&>p>img]:rounded-lg [&>p>img]:my-8',
        '[&>p>img]:shadow-mystical [&>p>img]:border [&>p>img]:border-mystical-200 [&>p>img]:dark:border-dark-600',

        // 链接样式
        '[&>p>a]:text-mystical-600 [&>p>a]:dark:text-mystical-400',
        '[&>p>a]:underline [&>p>a]:decoration-mystical-300 [&>p>a]:underline-offset-2',
        '[&>p>a]:decoration-1 [&>p>a]:transition-colors',
        '[&>p>a:hover]:text-mystical-700 [&>p>a:hover]:dark:text-mystical-300',
        '[&>p>a:hover]:decoration-mystical-500',

        // 分隔线样式
        '[&>hr]:border-none [&>hr]:h-px [&>hr]:my-12',
        '[&>hr]:bg-gradient-to-r [&>hr]:from-transparent [&>hr]:via-mystical-300 [&>hr]:to-transparent',

        // 表格样式
        '[&>table]:w-full [&>table]:my-8 [&>table]:border-collapse',
        '[&>table]:border [&>table]:border-mystical-200 [&>table]:dark:border-dark-600',
        '[&>table]:rounded-lg [&>table]:overflow-hidden',
        
        '[&>table>thead]:bg-mystical-50 [&>table>thead]:dark:bg-dark-800',
        '[&>table>thead>tr>th]:px-4 [&>table>thead>tr>th]:py-3 [&>table>thead>tr>th]:text-left',
        '[&>table>thead>tr>th]:font-semibold [&>table>thead>tr>th]:text-mystical-900 [&>table>thead>tr>th]:dark:text-white',
        
        '[&>table>tbody>tr]:border-t [&>table>tbody>tr]:border-mystical-200 [&>table>tbody>tr]:dark:border-dark-600',
        '[&>table>tbody>tr>td]:px-4 [&>table>tbody>tr>td]:py-3 [&>table>tbody>tr>td]:text-mystical-800 [&>table>tbody>tr>td]:dark:text-mystical-200',

        className
      )}
      style={{
        // Medium标准的阅读体验
        fontSize: '1.25rem',        // 20px - Medium标准字体大小
        lineHeight: '1.75',         // 1.75倍行高 - Medium标准
        fontFamily: 'Georgia, serif', // Medium使用的字体
      }}
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}

/**
 * 为HTML标题添加ID属性，用于锚点导航
 */
function addHeadingIds(htmlContent: string): string {
  return htmlContent.replace(
    /<h([1-6])([^>]*)>([^<]+)<\/h[1-6]>/gi,
    (match, level, attributes, title) => {
      // 检查是否已经有ID
      if (attributes.includes('id=')) {
        return match;
      }

      // 生成ID
      const id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

      return `<h${level}${attributes} id="${id}">${title}</h${level}>`;
    }
  );
}

/**
 * 检测内容格式
 */
export function detectContentFormat(content: string): 'markdown' | 'html' | 'text' {
  // 检查是否包含HTML标签
  if (/<[^>]+>/.test(content)) {
    return 'html';
  }
  
  // 检查是否包含Markdown语法
  if (/^#{1,6}\s/.test(content) || /\*\*.*\*\*/.test(content) || /\[.*\]\(.*\)/.test(content)) {
    return 'markdown';
  }
  
  return 'text';
}
