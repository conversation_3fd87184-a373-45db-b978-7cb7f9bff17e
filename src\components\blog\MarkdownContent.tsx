'use client';

import React, { useMemo } from 'react';
import { remark } from 'remark';
import remarkGfm from 'remark-gfm';
import remarkHtml from 'remark-html';
import { cn } from '@/lib/utils';

interface MarkdownContentProps {
  content: string;
  className?: string;
}

/**
 * Markdown内容渲染组件
 * 基于01-frontend-design-rules.md的博客文章设计规范
 */
export function MarkdownContent({ content, className }: MarkdownContentProps) {
  // 处理Markdown内容
  const processedContent = useMemo(() => {
    try {
      // 检查内容是否已经是HTML格式
      if (content.includes('<') && content.includes('>')) {
        // 如果已经是HTML，直接返回并添加标题ID
        return addHeadingIds(content);
      }

      // 如果是Markdown，转换为HTML
      const result = remark()
        .use(remarkGfm) // 支持GitHub Flavored Markdown
        .use(remarkHtml, { sanitize: false }) // 允许HTML标签
        .processSync(content);

      return addHeadingIds(String(result));
    } catch (error) {
      console.error('Error processing markdown:', error);
      // 如果处理失败，返回原始内容
      return content;
    }
  }, [content]);

  return (
    <article
      id="article"
      className={cn(
        // Medium风格的基础样式
        'prose prose-lg max-w-none',
        
        // 标题样式 - 基于最佳阅读体验规范
        // H1: mobile: 24-28px, desktop: 28-32px
        '[&>h1]:font-serif [&>h1]:font-bold [&>h1]:text-mystical-900 [&>h1]:dark:text-white',
        '[&>h1]:text-2xl [&>h1]:md:text-3xl [&>h1]:mt-12 [&>h1]:mb-6 [&>h1]:leading-tight [&>h1]:tracking-tight',

        // H2: mobile: 20-24px, desktop: 24-28px
        '[&>h2]:font-serif [&>h2]:font-bold [&>h2]:text-mystical-900 [&>h2]:dark:text-white',
        '[&>h2]:text-xl [&>h2]:md:text-2xl [&>h2]:mt-10 [&>h2]:mb-4 [&>h2]:leading-tight [&>h2]:tracking-tight',

        // H3: mobile: 18-20px, desktop: 20-22px
        '[&>h3]:font-serif [&>h3]:font-semibold [&>h3]:text-mystical-800 [&>h3]:dark:text-mystical-100',
        '[&>h3]:text-lg [&>h3]:md:text-xl [&>h3]:mt-8 [&>h3]:mb-4 [&>h3]:leading-snug',

        // H4: mobile: 16-18px, desktop: 18-20px
        '[&>h4]:font-serif [&>h4]:font-semibold [&>h4]:text-mystical-800 [&>h4]:dark:text-mystical-100',
        '[&>h4]:text-base [&>h4]:md:text-lg [&>h4]:mt-6 [&>h4]:mb-3 [&>h4]:leading-snug',

        '[&>h5]:font-serif [&>h5]:font-semibold [&>h5]:text-mystical-800 [&>h5]:dark:text-mystical-100',
        '[&>h5]:text-lg [&>h5]:mt-4 [&>h5]:mb-2',

        '[&>h6]:font-serif [&>h6]:font-semibold [&>h6]:text-mystical-800 [&>h6]:dark:text-mystical-100',
        '[&>h6]:text-base [&>h6]:mt-4 [&>h6]:mb-2',

        // 段落样式 - 基于最佳阅读体验规范
        // 中文: mobile 15-17px, desktop 16-18px, 行高: 1.6-1.8
        // 英文: mobile 16-18px, desktop 18-21px, 行高: 1.5-1.6
        '[&>p]:mb-6 [&>p]:text-mystical-800 [&>p]:dark:text-mystical-100',
        '[&>p]:text-base [&>p]:md:text-lg [&>p]:font-serif',
        '[&>p]:leading-relaxed [&>p]:md:leading-relaxed', // 1.625 (26px/16px) ≈ 1.6

        // 首段特殊样式 - 稍大一些突出重点
        '[&>p:first-of-type]:text-lg [&>p:first-of-type]:md:text-xl [&>p:first-of-type]:font-normal [&>p:first-of-type]:text-mystical-900 [&>p:first-of-type]:dark:text-white',

        // 列表样式 - 基于最佳阅读体验规范
        '[&>ul]:mb-6 [&>ul]:pl-6',
        '[&>ul>li]:mb-2 [&>ul>li]:text-base [&>ul>li]:md:text-lg [&>ul>li]:leading-relaxed [&>ul>li]:text-mystical-800 [&>ul>li]:dark:text-mystical-100',
        '[&>ul>li]:list-disc [&>ul>li]:marker:text-mystical-500',

        '[&>ol]:mb-6 [&>ol]:pl-6',
        '[&>ol>li]:mb-2 [&>ol>li]:text-base [&>ol>li]:md:text-lg [&>ol>li]:leading-relaxed [&>ol>li]:text-mystical-800 [&>ol>li]:dark:text-mystical-100',
        '[&>ol>li]:list-decimal [&>ol>li]:marker:text-mystical-500 [&>ol>li]:marker:font-semibold',

        // 引用块样式 - 基于最佳阅读体验规范
        // mobile: 17-18px, desktop: 18-20px, 行高: 1.6
        '[&>blockquote]:text-lg [&>blockquote]:md:text-xl [&>blockquote]:font-serif [&>blockquote]:italic',
        '[&>blockquote]:text-mystical-700 [&>blockquote]:dark:text-mystical-200',
        '[&>blockquote]:px-6 [&>blockquote]:md:px-8 [&>blockquote]:py-6 [&>blockquote]:my-8',
        '[&>blockquote]:bg-mystical-50 [&>blockquote]:dark:bg-dark-800',
        '[&>blockquote]:border-l-4 [&>blockquote]:border-mystical-400',
        '[&>blockquote]:rounded-r-lg [&>blockquote]:leading-relaxed',

        // 代码样式
        '[&>pre]:bg-dark-900 [&>pre]:text-dark-100 [&>pre]:p-6 [&>pre]:rounded-lg',
        '[&>pre]:my-8 [&>pre]:overflow-auto [&>pre]:border [&>pre]:border-dark-700',
        '[&>pre]:text-sm [&>pre]:font-mono [&>pre]:leading-relaxed',
        
        '[&>p>code]:bg-mystical-100 [&>p>code]:dark:bg-dark-700',
        '[&>p>code]:text-mystical-800 [&>p>code]:dark:text-mystical-200',
        '[&>p>code]:px-2 [&>p>code]:py-1 [&>p>code]:rounded [&>p>code]:text-sm',
        '[&>p>code]:font-mono [&>p>code]:border [&>p>code]:border-mystical-200 [&>p>code]:dark:border-dark-600',

        // 图片样式
        '[&>p>img]:w-full [&>p>img]:h-auto [&>p>img]:rounded-lg [&>p>img]:my-8',
        '[&>p>img]:shadow-mystical [&>p>img]:border [&>p>img]:border-mystical-200 [&>p>img]:dark:border-dark-600',

        // 链接样式
        '[&>p>a]:text-mystical-600 [&>p>a]:dark:text-mystical-400',
        '[&>p>a]:underline [&>p>a]:decoration-mystical-300 [&>p>a]:underline-offset-2',
        '[&>p>a]:decoration-1 [&>p>a]:transition-colors',
        '[&>p>a:hover]:text-mystical-700 [&>p>a:hover]:dark:text-mystical-300',
        '[&>p>a:hover]:decoration-mystical-500',

        // 分隔线样式
        '[&>hr]:border-none [&>hr]:h-px [&>hr]:my-12',
        '[&>hr]:bg-gradient-to-r [&>hr]:from-transparent [&>hr]:via-mystical-300 [&>hr]:to-transparent',

        // 表格样式
        '[&>table]:w-full [&>table]:my-8 [&>table]:border-collapse',
        '[&>table]:border [&>table]:border-mystical-200 [&>table]:dark:border-dark-600',
        '[&>table]:rounded-lg [&>table]:overflow-hidden',
        
        '[&>table>thead]:bg-mystical-50 [&>table>thead]:dark:bg-dark-800',
        '[&>table>thead>tr>th]:px-4 [&>table>thead>tr>th]:py-3 [&>table>thead>tr>th]:text-left',
        '[&>table>thead>tr>th]:font-semibold [&>table>thead>tr>th]:text-mystical-900 [&>table>thead>tr>th]:dark:text-white',
        
        '[&>table>tbody>tr]:border-t [&>table>tbody>tr]:border-mystical-200 [&>table>tbody>tr]:dark:border-dark-600',
        '[&>table>tbody>tr>td]:px-4 [&>table>tbody>tr>td]:py-3 [&>table>tbody>tr>td]:text-mystical-800 [&>table>tbody>tr>td]:dark:text-mystical-200',

        className
      )}
      style={{
        // 基于最佳阅读体验的字体设置
        fontSize: '1rem',           // 16px - 基础字体大小，通过CSS类控制响应式
        lineHeight: '1.625',        // 1.625倍行高 - 适合中英文混排
        fontFamily: 'Georgia, "Crimson Text", "PT Serif", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", serif', // 中英文混排字体栈
        color: '#333333',           // 深灰色而非纯黑，更舒适
      }}
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}

{/* 添加自定义CSS来优化字体渲染 */}
<style jsx global>{`
  /* 优化中英文混排的字体渲染 */
  article {
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 移动端字体优化 */
  @media (max-width: 768px) {
    article {
      font-size: 15px !important; /* 中文移动端 15-17px */
      line-height: 1.7 !important; /* 中文需要更大行距 */
    }

    article h1 {
      font-size: 24px !important;
      line-height: 1.3 !important;
    }

    article h2 {
      font-size: 20px !important;
      line-height: 1.4 !important;
    }

    article h3 {
      font-size: 18px !important;
      line-height: 1.4 !important;
    }
  }

  /* 桌面端字体优化 */
  @media (min-width: 769px) {
    article {
      font-size: 18px !important; /* 桌面端 18-21px */
      line-height: 1.6 !important;
    }

    article h1 {
      font-size: 32px !important;
      line-height: 1.2 !important;
    }

    article h2 {
      font-size: 28px !important;
      line-height: 1.3 !important;
    }

    article h3 {
      font-size: 22px !important;
      line-height: 1.4 !important;
    }
  }

  /* 英文内容优化 */
  article[lang="en"] {
    font-family: "Inter", "Source Sans Pro", "DM Sans", system-ui, sans-serif;
  }

  @media (max-width: 768px) {
    article[lang="en"] {
      font-size: 16px !important; /* 英文移动端 16-18px */
      line-height: 1.6 !important;
    }
  }

  @media (min-width: 769px) {
    article[lang="en"] {
      font-size: 19px !important; /* 英文桌面端 18-21px */
      line-height: 1.55 !important;
    }
  }
`}</style>
  );
}

/**
 * 为HTML标题添加ID属性，用于锚点导航
 */
function addHeadingIds(htmlContent: string): string {
  return htmlContent.replace(
    /<h([1-6])([^>]*)>([^<]+)<\/h[1-6]>/gi,
    (match, level, attributes, title) => {
      // 检查是否已经有ID
      if (attributes.includes('id=')) {
        return match;
      }

      // 生成ID
      const id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

      return `<h${level}${attributes} id="${id}">${title}</h${level}>`;
    }
  );
}

/**
 * 检测内容格式
 */
export function detectContentFormat(content: string): 'markdown' | 'html' | 'text' {
  // 检查是否包含HTML标签
  if (/<[^>]+>/.test(content)) {
    return 'html';
  }
  
  // 检查是否包含Markdown语法
  if (/^#{1,6}\s/.test(content) || /\*\*.*\*\*/.test(content) || /\[.*\]\(.*\)/.test(content)) {
    return 'markdown';
  }
  
  return 'text';
}
