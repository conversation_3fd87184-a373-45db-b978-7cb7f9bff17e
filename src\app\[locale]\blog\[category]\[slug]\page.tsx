import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';
import { BlogPostClient } from '@/components/blog/BlogPostClient';
import { generateTableOfContents } from '@/lib/table-of-contents';
import { BlogPost, Locale } from '@/types';
import { Heart, Share2 } from 'lucide-react';
import { DatabaseService } from '@/lib/database-service';
import { MarkdownContent } from '@/components/blog/MarkdownContent';
import { MobileTableOfContents } from '@/components/blog/TableOfContents';
import { ArticleFooter } from '@/components/blog/ArticleFooter';

interface BlogPostPageProps {
  params: {
    locale: string;
    category: string;
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getPostBySlug(params.category, params.slug, params.locale as Locale);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  const description = post.seoDescription || post.excerpt || '';

  return {
    title: post.seoTitle || post.title,
    description,
    keywords: post.keywords,
    authors: [{ name: 'Author' }],
    openGraph: {
      title: post.seoTitle || post.title,
      description,
      type: 'article',
      locale: params.locale,
      publishedTime: post.publishedAt?.toISOString(),
      modifiedTime: post.updatedAt.toISOString(),
      authors: ['Author'],
      images: post.coverImage ? [{ url: post.coverImage, alt: post.title }] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seoTitle || post.title,
      description,
      images: post.coverImage ? [post.coverImage] : [],
    },
  };
}

// 数据获取函数
async function getPostBySlug(
  category: string,
  slug: string,
  locale: Locale
): Promise<BlogPost | null> {
  try {
    const post = await DatabaseService.getBlogPostBySlug(slug, locale);

    // 验证分类是否匹配
    if (post && post.category !== category) {
      return null;
    }

    return post;
  } catch (error) {
    console.error('Error fetching post:', error);
    return null;
  }
}

async function getRelatedPosts(
  postId: string,
  categorySlug: string,
  locale: Locale,
  limit: number = 3
): Promise<BlogPost[]> {
  try {
    const posts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      category: categorySlug,
      limit: limit + 1, // 多获取一个，以防包含当前文章
    });

    // 排除当前文章
    return posts.filter(post => post.id !== postId).slice(0, limit);
  } catch (error) {
    console.error('Error fetching related posts:', error);
    return [];
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const post = await getPostBySlug(params.category, params.slug, locale);

  if (!post) {
    notFound();
  }

  const relatedPosts = await getRelatedPosts(post.id, params.category, locale);
  const tableOfContents = generateTableOfContents(post.content);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 客户端交互组件 */}
      <BlogPostClient
        post={post}
        tableOfContents={tableOfContents}
        siteUrl={process.env['NEXT_PUBLIC_SITE_URL'] || 'https://example.com'}
        locale={locale}
      />

      {/* Medium风格的文章头部 */}
      <header className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
        {/* 面包屑导航 */}
        <nav className="flex items-center space-x-2 text-sm text-mystical-500 dark:text-mystical-400 mb-8">
          <Link href={`/${locale}/blog`} className="hover:text-mystical-700 dark:hover:text-mystical-300">
            Blog
          </Link>
          <span>/</span>
          <Link
            href={`/${locale}/blog?category=${post.category}`}
            className="hover:text-mystical-700 dark:hover:text-mystical-300 capitalize"
          >
            {post.category}
          </Link>
        </nav>

        {/* 文章标题 - Medium风格 */}
        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold font-serif text-mystical-900 dark:text-white mb-6 leading-tight tracking-tight">
          {post.title}
        </h1>

        {/* 文章摘要 */}
        {post.excerpt && (
          <p className="text-xl text-mystical-600 dark:text-mystical-300 mb-8 leading-relaxed">
            {post.excerpt}
          </p>
        )}

        {/* 作者信息和元数据 - Medium风格 */}
        <div className="flex items-center justify-between py-4 border-b border-mystical-200 dark:border-dark-700">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-mystical-200 dark:bg-dark-700 rounded-full flex items-center justify-center">
              <span className="text-mystical-600 dark:text-mystical-400 font-medium">A</span>
            </div>
            <div>
              <p className="font-medium text-mystical-900 dark:text-white">
                Author
              </p>
              <div className="flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400">
                <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                <span>·</span>
                <span>{post.readingTime} min read</span>
              </div>
            </div>
          </div>

          {/* 互动按钮 */}
          <div className="flex items-center gap-2">
            <button className="p-2 rounded-full hover:bg-mystical-100 dark:hover:bg-dark-700 transition-colors">
              <Heart className="w-5 h-5 text-mystical-500 dark:text-mystical-400" />
            </button>
            <button className="p-2 rounded-full hover:bg-mystical-100 dark:hover:bg-dark-700 transition-colors">
              <Share2 className="w-5 h-5 text-mystical-500 dark:text-mystical-400" />
            </button>
          </div>
        </div>
      </header>

      {/* 封面图片 - Medium风格 */}
      {post.coverImage && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mb-12">
          <Image
            src={post.coverImage}
            alt={post.title}
            width={1200}
            height={675}
            className="w-full aspect-[16/9] object-cover rounded-lg"
            priority
          />
        </div>
      )}

      {/* 主要内容区域 - Medium风格布局 */}
      <div className="relative">
        {/* 文章内容容器 */}
        <div className="max-w-[680px] mx-auto px-4 sm:px-6 lg:px-8 pb-16">

          {/* 移动端目录 */}
          {tableOfContents.length > 0 && (
            <MobileTableOfContents
              items={tableOfContents}
              className="mb-8"
            />
          )}

          {/* 文章正文 - 使用MarkdownContent组件渲染 */}
          <MarkdownContent content={post.content} />

          {/* 文章底部 */}
          <div className="mt-16">
            <ArticleFooter post={post} relatedPosts={relatedPosts} />
          </div>
        </div>
      </div>
    </div>
  );
}


